import { defineConfig } from 'vite'
import { resolve } from 'path'

// Bundle optimization configuration for Vite
export const optimizationConfig = {
  build: {
    // Enable tree shaking
    rollupOptions: {
      output: {
        // Manual chunk splitting for better caching
        manualChunks: {
          // Vendor chunks
          'vendor-react': ['react', 'react-dom', 'react-router-dom'],
          'vendor-ui': ['@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu', '@radix-ui/react-tooltip'],
          'vendor-query': ['@tanstack/react-query'],
          'vendor-monaco': ['@monaco-editor/react', 'monaco-editor'],
          
          // Feature chunks
          'feature-dashboard': ['./src/pages/Dashboard.tsx'],
          'feature-settings': ['./src/pages/Settings.tsx'],
          'feature-history': ['./src/pages/History.tsx'],
          
          // Utility chunks
          'utils': ['./src/lib/utils.ts', './src/utils/cache.ts'],
          'components-ui': [
            './src/components/ui/button.tsx',
            './src/components/ui/input.tsx',
            './src/components/ui/card.tsx'
          ]
        },
        
        // Optimize chunk names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
          if (facadeModuleId) {
            const name = facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '')
            return `chunks/${name}-[hash].js`
          }
          return 'chunks/[name]-[hash].js'
        },
        
        // Optimize asset names
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || []
          const ext = info[info.length - 1]
          
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext)) {
            return `assets/images/[name]-[hash][extname]`
          }
          if (/woff2?|eot|ttf|otf/i.test(ext)) {
            return `assets/fonts/[name]-[hash][extname]`
          }
          return `assets/[name]-[hash][extname]`
        }
      },
      
      // External dependencies (for CDN usage if needed)
      external: (id) => {
        // Keep all dependencies bundled for now
        // Can be configured to use CDN for large libraries
        return false
      }
    },
    
    // Minification settings
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.log in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
        passes: 2
      },
      mangle: {
        safari10: true
      },
      format: {
        comments: false
      }
    },
    
    // Source map settings
    sourcemap: process.env.NODE_ENV === 'development',
    
    // Chunk size warnings
    chunkSizeWarningLimit: 1000,
    
    // Asset inlining threshold
    assetsInlineLimit: 4096,
    
    // CSS code splitting
    cssCodeSplit: true,
    
    // Target modern browsers for smaller bundles
    target: ['es2020', 'chrome80', 'firefox78', 'safari14', 'edge88']
  },
  
  // Dependency optimization
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@tanstack/react-query'
    ],
    exclude: [
      // Large dependencies that should be lazy loaded
      '@monaco-editor/react',
      'monaco-editor'
    ]
  },
  
  // Define global constants for tree shaking
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    __PROD__: process.env.NODE_ENV === 'production',
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0')
  }
}

// Bundle analyzer configuration
export const bundleAnalyzerConfig = {
  plugins: [
    // Add bundle analyzer plugin for development
    ...(process.env.ANALYZE === 'true' ? [
      {
        name: 'bundle-analyzer',
        generateBundle() {
          // This would integrate with rollup-plugin-visualizer
          console.log('Bundle analysis enabled')
        }
      }
    ] : [])
  ]
}

// Performance budget configuration
export const performanceBudget = {
  // Maximum bundle sizes
  maxBundleSize: 500 * 1024, // 500KB
  maxChunkSize: 200 * 1024,  // 200KB
  maxAssetSize: 100 * 1024,  // 100KB
  
  // Performance thresholds
  thresholds: {
    fcp: 1500,  // First Contentful Paint
    lcp: 2500,  // Largest Contentful Paint
    fid: 100,   // First Input Delay
    cls: 0.1,   // Cumulative Layout Shift
    ttfb: 600   // Time to First Byte
  }
}

// Tree shaking configuration
export const treeShakingConfig = {
  // Ensure proper tree shaking for common libraries
  sideEffects: false,
  
  // Mark specific modules as side-effect free
  modulesSideEffects: {
    'lodash': false,
    'date-fns': false,
    'ramda': false
  }
}

// Code splitting strategies
export const codeSplittingStrategies = {
  // Route-based splitting
  routes: {
    home: () => import('./src/pages/Index'),
    dashboard: () => import('./src/pages/Dashboard'),
    settings: () => import('./src/pages/Settings'),
    history: () => import('./src/pages/History')
  },
  
  // Feature-based splitting
  features: {
    codeEditor: () => import('./src/components/CodeEditor'),
    charts: () => import('./src/components/charts'),
    advanced: () => import('./src/components/advanced')
  },
  
  // Library-based splitting
  libraries: {
    monaco: () => import('@monaco-editor/react'),
    charts: () => import('recharts'),
    animations: () => import('framer-motion')
  }
}

// Asset optimization
export const assetOptimization = {
  // Image optimization
  images: {
    formats: ['webp', 'avif', 'png', 'jpg'],
    sizes: [320, 640, 960, 1280, 1920],
    quality: 80
  },
  
  // Font optimization
  fonts: {
    preload: [
      'Inter-Regular.woff2',
      'Inter-Medium.woff2',
      'JetBrainsMono-Regular.woff2'
    ],
    display: 'swap'
  },
  
  // Icon optimization
  icons: {
    sprite: true,
    inline: true,
    svgo: {
      plugins: [
        'removeViewBox',
        'removeDimensions',
        'removeTitle',
        'removeDesc'
      ]
    }
  }
}

// Development optimizations
export const devOptimizations = {
  // Fast refresh
  hmr: true,
  
  // Source map type for development
  devtool: 'eval-cheap-module-source-map',
  
  // Cache configuration
  cache: {
    type: 'filesystem',
    buildDependencies: {
      config: [__filename]
    }
  }
}

// Production optimizations
export const prodOptimizations = {
  // Compression
  compression: {
    gzip: true,
    brotli: true,
    threshold: 1024
  },
  
  // Caching headers
  caching: {
    immutable: /\.(js|css|png|jpg|jpeg|gif|svg|woff2?)$/,
    maxAge: {
      html: 0,
      assets: 31536000, // 1 year
      api: 300 // 5 minutes
    }
  },
  
  // Preloading strategies
  preload: {
    critical: ['vendor-react', 'vendor-ui'],
    prefetch: ['feature-dashboard', 'feature-settings']
  }
}

// Bundle size monitoring
export function monitorBundleSize() {
  if (process.env.NODE_ENV === 'production') {
    // This would integrate with your monitoring service
    console.log('Bundle size monitoring enabled')
  }
}

// Webpack bundle analyzer equivalent for Vite
export function analyzeBundleComposition() {
  if (process.env.ANALYZE === 'true') {
    import('rollup-plugin-visualizer').then(({ visualizer }) => {
      console.log('Bundle analyzer available at stats.html')
    }).catch(() => {
      console.log('Bundle analyzer not available')
    })
  }
}
