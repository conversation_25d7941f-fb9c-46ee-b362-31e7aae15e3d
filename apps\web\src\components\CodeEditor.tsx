
import { useEffect, useRef, useState } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import { useTheme } from '@/components/theme-provider';

interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language: string;
  ariaLabel?: string;
  ariaDescribedBy?: string;
  readOnly?: boolean;
  height?: string | number;
  showMinimap?: boolean;
  enableAutocomplete?: boolean;
  enableCodeFolding?: boolean;
  enableFind?: boolean;
  onSave?: () => void;
}

export const CodeEditor = ({
  value,
  onChange,
  language,
  ariaLabel = "Code editor",
  ariaDescribedBy,
  readOnly = false,
  height = "100%",
  showMinimap,
  enableAutocomplete = true,
  enableCodeFolding = true,
  enableFind = true,
  onSave
}: CodeEditorProps) => {
  const editorRef = useRef<any>(null);
  const monacoRef = useRef<Monaco | null>(null);
  const { theme } = useTheme();
  const [isReady, setIsReady] = useState(false);

  // Default editor settings (can be overridden by props)
  const defaultSettings = {
    fontSize: 14,
    tabSize: 2,
    wordWrap: true,
    minimap: false
  };

  // Configure Monaco before editor mounts
  const handleBeforeMount = (monaco: Monaco) => {
    monacoRef.current = monaco;

    // Register custom themes
    monaco.editor.defineTheme('metamorphic-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955', fontStyle: 'italic' },
        { token: 'keyword', foreground: '569CD6', fontStyle: 'bold' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' },
        { token: 'type', foreground: '4EC9B0' },
        { token: 'function', foreground: 'DCDCAA' },
        { token: 'variable', foreground: '9CDCFE' },
      ],
      colors: {
        'editor.background': '#0f172a', // slate-900
        'editor.foreground': '#f8fafc', // slate-50
        'editor.lineHighlightBackground': '#1e293b', // slate-800
        'editor.selectionBackground': '#3730a3', // indigo-700
        'editorCursor.foreground': '#6366f1', // indigo-500
        'editorLineNumber.foreground': '#64748b', // slate-500
        'editorLineNumber.activeForeground': '#f8fafc', // slate-50
      }
    });

    monaco.editor.defineTheme('metamorphic-light', {
      base: 'vs',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '008000', fontStyle: 'italic' },
        { token: 'keyword', foreground: '0000FF', fontStyle: 'bold' },
        { token: 'string', foreground: 'A31515' },
        { token: 'number', foreground: '098658' },
        { token: 'type', foreground: '267F99' },
        { token: 'function', foreground: '795E26' },
        { token: 'variable', foreground: '001080' },
      ],
      colors: {
        'editor.background': '#ffffff',
        'editor.foreground': '#000000',
        'editor.lineHighlightBackground': '#f5f5f5',
        'editor.selectionBackground': '#ADD6FF',
        'editorCursor.foreground': '#6366f1', // indigo-500
      }
    });

    // Enhanced autocomplete for JavaScript/TypeScript
    if (language === 'javascript' || language === 'typescript') {
      monaco.languages.registerCompletionItemProvider(language, {
        provideCompletionItems: (model, position) => {
          const suggestions = [
            {
              label: 'console.log',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: 'console.log(${1:message});',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Log a message to the console'
            },
            {
              label: 'function',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: 'function ${1:name}(${2:params}) {\n\t${3:// body}\n}',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Create a function'
            },
            {
              label: 'arrow function',
              kind: monaco.languages.CompletionItemKind.Snippet,
              insertText: '(${1:params}) => {\n\t${2:// body}\n}',
              insertTextRules: monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet,
              documentation: 'Create an arrow function'
            }
          ];
          return { suggestions };
        }
      });
    }
  };

  const handleEditorDidMount = (editor: any, monaco: Monaco) => {
    editorRef.current = editor;
    setIsReady(true);

    // Configure editor settings
    editor.updateOptions({
      fontSize: defaultSettings.fontSize,
      minimap: { enabled: showMinimap ?? defaultSettings.minimap },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      tabSize: defaultSettings.tabSize,
      wordWrap: defaultSettings.wordWrap ? 'on' : 'off',
      lineNumbers: 'on',
      glyphMargin: false,
      folding: enableCodeFolding,
      lineDecorationsWidth: 10,
      lineNumbersMinChars: 3,
      readOnly,
      find: {
        addExtraSpaceOnTop: false,
        autoFindInSelection: 'never',
        seedSearchStringFromSelection: 'always'
      }
    });

    // Add keyboard shortcuts
    if (onSave) {
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
        onSave();
      });
    }

    // Add find functionality
    if (enableFind) {
      editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyF, () => {
        editor.getAction('actions.find').run();
      });
    }

    // Add format document
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyMod.Shift | monaco.KeyCode.KeyF, () => {
      editor.getAction('editor.action.formatDocument').run();
    });

    // Add comment toggle
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.Slash, () => {
      editor.getAction('editor.action.commentLine').run();
    });
  };

  // Update editor settings when props change
  useEffect(() => {
    if (editorRef.current && isReady) {
      editorRef.current.updateOptions({
        fontSize: defaultSettings.fontSize,
        minimap: { enabled: showMinimap ?? defaultSettings.minimap },
        tabSize: defaultSettings.tabSize,
        wordWrap: defaultSettings.wordWrap ? 'on' : 'off',
      });
    }
  }, [showMinimap, isReady]);

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      onChange(value);
    }
  };

  // Determine theme based on current theme
  const getEditorTheme = () => {
    if (theme === 'light') return 'metamorphic-light';
    return 'metamorphic-dark';
  };

  return (
    <div
      className="h-full bg-background border border-border rounded-lg overflow-hidden"
      role="region"
      aria-label={ariaLabel}
      aria-describedby={ariaDescribedBy}
      style={{ height }}
    >
      <Editor
        height="100%"
        defaultLanguage={language}
        language={language}
        value={value}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        beforeMount={handleBeforeMount}
        theme={getEditorTheme()}
        options={{
          padding: { top: 16, bottom: 16 },
          fontFamily: 'JetBrains Mono, SF Mono, Monaco, Inconsolata, Roboto Mono, Consolas, monospace',
          fontLigatures: true,
          cursorBlinking: 'smooth',
          renderWhitespace: 'selection',
          smoothScrolling: true,
          contextmenu: true,
          selectOnLineNumbers: true,
          roundedSelection: false,
          readOnly,
          cursorStyle: 'line',
          automaticLayout: true,

          // Enhanced features
          suggest: {
            enabled: enableAutocomplete,
            showKeywords: true,
            showSnippets: true,
            showFunctions: true,
            showConstructors: true,
            showFields: true,
            showVariables: true,
            showClasses: true,
            showStructs: true,
            showInterfaces: true,
            showModules: true,
            showProperties: true,
            showEvents: true,
            showOperators: true,
            showUnits: true,
            showValues: true,
            showConstants: true,
            showEnums: true,
            showEnumMembers: true,
            showColors: true,
            showFiles: true,
            showReferences: true,
            showFolders: true,
            showTypeParameters: true,
          },

          quickSuggestions: {
            other: enableAutocomplete,
            comments: false,
            strings: false
          },

          parameterHints: {
            enabled: enableAutocomplete
          },

          // Accessibility options
          accessibilitySupport: 'on',
          ariaLabel: ariaLabel,

          // Performance optimizations
          renderValidationDecorations: 'on',
          renderLineHighlight: 'line',
          renderLineHighlightOnlyWhenFocus: false,
          hideCursorInOverviewRuler: false,
          scrollbar: {
            useShadows: false,
            verticalHasArrows: false,
            horizontalHasArrows: false,
            vertical: 'visible',
            horizontal: 'visible',
            verticalScrollbarSize: 10,
            horizontalScrollbarSize: 10,
          },

          // Code formatting
          formatOnPaste: true,
          formatOnType: true,

          // Bracket matching
          matchBrackets: 'always',
          bracketPairColorization: {
            enabled: true
          },

          // Indentation guides
          renderIndentGuides: true,
          highlightActiveIndentGuide: true,

          // Word navigation
          wordSeparators: '`~!@#$%^&*()-=+[{]}\\|;:\'",.<>/?',

          // Mouse behavior
          mouseWheelZoom: true,
          multiCursorModifier: 'ctrlCmd',

          // Hover
          hover: {
            enabled: true,
            delay: 300,
            sticky: true
          }
        }}
      />
    </div>
  );
};
