import * as React from "react"
import { cn } from "@/lib/utils"
import { ChevronLeftIcon, ChevronRightIcon, MoreVerticalIcon } from "@/components/ui/icon"
import { Button } from "@/components/ui/button"
import { VStack, HStack } from "@/components/ui/layout"
import { Typography } from "@/components/ui/typography"

// Mobile-first responsive container
interface MobileContainerProps {
  children: React.ReactNode
  className?: string
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export function MobileContainer({ 
  children, 
  className, 
  maxWidth = 'lg',
  padding = 'md' 
}: MobileContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-4xl',
    xl: 'max-w-6xl',
    full: 'max-w-full'
  }

  const paddingClasses = {
    none: '',
    sm: 'px-4 py-2',
    md: 'px-4 sm:px-6 py-4',
    lg: 'px-4 sm:px-6 lg:px-8 py-6'
  }

  return (
    <div className={cn(
      "w-full mx-auto",
      maxWidthClasses[maxWidth],
      paddingClasses[padding],
      className
    )}>
      {children}
    </div>
  )
}

// Mobile navigation header
interface MobileHeaderProps {
  title: string
  subtitle?: string
  onBack?: () => void
  actions?: React.ReactNode
  className?: string
}

export function MobileHeader({ 
  title, 
  subtitle, 
  onBack, 
  actions, 
  className 
}: MobileHeaderProps) {
  return (
    <header className={cn(
      "sticky top-0 z-40 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
      className
    )}>
      <div className="flex h-14 items-center px-4">
        <HStack spacing="sm" className="flex-1 min-w-0">
          {onBack && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onBack}
              className="h-8 w-8 p-0 shrink-0"
            >
              <ChevronLeftIcon />
            </Button>
          )}
          
          <VStack spacing="none" className="flex-1 min-w-0">
            <Typography 
              variant="h6" 
              className="truncate text-foreground"
            >
              {title}
            </Typography>
            {subtitle && (
              <Typography 
                variant="caption" 
                className="truncate text-muted-foreground"
              >
                {subtitle}
              </Typography>
            )}
          </VStack>
          
          {actions && (
            <div className="shrink-0">
              {actions}
            </div>
          )}
        </HStack>
      </div>
    </header>
  )
}

// Mobile bottom navigation
interface MobileBottomNavItem {
  id: string
  label: string
  icon: React.ReactNode
  badge?: string | number
  disabled?: boolean
}

interface MobileBottomNavProps {
  items: MobileBottomNavItem[]
  activeId: string
  onItemClick: (id: string) => void
  className?: string
}

export function MobileBottomNav({ 
  items, 
  activeId, 
  onItemClick, 
  className 
}: MobileBottomNavProps) {
  return (
    <nav className={cn(
      "fixed bottom-0 left-0 right-0 z-40 border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 safe-area-inset-bottom",
      className
    )}>
      <div className="grid grid-cols-2 sm:grid-cols-4 h-16">
        {items.map((item) => (
          <button
            key={item.id}
            onClick={() => !item.disabled && onItemClick(item.id)}
            disabled={item.disabled}
            className={cn(
              "flex flex-col items-center justify-center space-y-1 transition-colors relative",
              "min-h-[44px] touch-manipulation", // Minimum touch target size
              {
                "text-primary": activeId === item.id,
                "text-muted-foreground hover:text-foreground": activeId !== item.id && !item.disabled,
                "text-muted-foreground/50": item.disabled
              }
            )}
          >
            <div className="relative">
              {item.icon}
              {item.badge && (
                <span className="absolute -top-1 -right-1 h-4 w-4 rounded-full bg-destructive text-destructive-foreground text-xs flex items-center justify-center">
                  {item.badge}
                </span>
              )}
            </div>
            <Typography variant="caption" className="text-xs">
              {item.label}
            </Typography>
          </button>
        ))}
      </div>
    </nav>
  )
}

// Mobile drawer/sheet
interface MobileDrawerProps {
  isOpen: boolean
  onClose: () => void
  children: React.ReactNode
  title?: string
  position?: 'bottom' | 'left' | 'right'
  className?: string
}

export function MobileDrawer({ 
  isOpen, 
  onClose, 
  children, 
  title, 
  position = 'bottom',
  className 
}: MobileDrawerProps) {
  React.useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }
    
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  if (!isOpen) return null

  const positionClasses = {
    bottom: 'bottom-0 left-0 right-0 rounded-t-lg max-h-[80vh]',
    left: 'top-0 left-0 bottom-0 w-80 max-w-[80vw] rounded-r-lg',
    right: 'top-0 right-0 bottom-0 w-80 max-w-[80vw] rounded-l-lg'
  }

  const slideClasses = {
    bottom: 'animate-in slide-in-from-bottom duration-300',
    left: 'animate-in slide-in-from-left duration-300',
    right: 'animate-in slide-in-from-right duration-300'
  }

  return (
    <div className="fixed inset-0 z-50">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-background/80 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Drawer */}
      <div className={cn(
        "absolute bg-card border border-border shadow-lg overflow-hidden",
        positionClasses[position],
        slideClasses[position],
        className
      )}>
        {title && (
          <div className="flex items-center justify-between p-4 border-b border-border">
            <Typography variant="h6" className="text-foreground">
              {title}
            </Typography>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              ×
            </Button>
          </div>
        )}
        
        <div className="overflow-y-auto max-h-full">
          {children}
        </div>
        
        {/* Handle for bottom drawer */}
        {position === 'bottom' && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-1 bg-muted-foreground/30 rounded-full" />
        )}
      </div>
    </div>
  )
}

// Mobile-optimized button
interface MobileTouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'
  size?: 'sm' | 'md' | 'lg'
  fullWidth?: boolean
  loading?: boolean
}

export function MobileTouchButton({
  variant = 'default',
  size = 'md',
  fullWidth = false,
  loading = false,
  className,
  children,
  disabled,
  ...props
}: MobileTouchButtonProps) {
  const sizeClasses = {
    sm: 'h-10 px-4 text-sm', // Minimum 44px touch target
    md: 'h-12 px-6 text-base',
    lg: 'h-14 px-8 text-lg'
  }

  return (
    <Button
      variant={variant}
      disabled={disabled || loading}
      className={cn(
        "touch-manipulation transition-transform active:scale-95",
        sizeClasses[size],
        fullWidth && "w-full",
        className
      )}
      {...props}
    >
      {loading ? (
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
          <span>Loading...</span>
        </div>
      ) : (
        children
      )}
    </Button>
  )
}

// Mobile card component
interface MobileCardProps {
  children: React.ReactNode
  className?: string
  padding?: 'sm' | 'md' | 'lg'
  interactive?: boolean
  onClick?: () => void
}

export function MobileCard({ 
  children, 
  className, 
  padding = 'md',
  interactive = false,
  onClick 
}: MobileCardProps) {
  const paddingClasses = {
    sm: 'p-3',
    md: 'p-4',
    lg: 'p-6'
  }

  return (
    <div
      className={cn(
        "bg-card border border-border rounded-lg shadow-sm",
        paddingClasses[padding],
        interactive && "touch-manipulation transition-transform active:scale-[0.98] cursor-pointer",
        className
      )}
      onClick={onClick}
    >
      {children}
    </div>
  )
}

// Mobile-optimized input
interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  fullWidth?: boolean
}

export function MobileInput({ 
  label, 
  error, 
  fullWidth = true, 
  className, 
  ...props 
}: MobileInputProps) {
  return (
    <VStack spacing="xs" className={fullWidth ? "w-full" : undefined}>
      {label && (
        <label className="text-sm font-medium text-foreground">
          {label}
        </label>
      )}
      <input
        className={cn(
          "h-12 w-full rounded-md border border-input bg-background px-4 py-3 text-base ring-offset-background",
          "placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "touch-manipulation", // Prevent zoom on iOS
          error && "border-destructive focus-visible:ring-destructive",
          className
        )}
        {...props}
      />
      {error && (
        <Typography variant="caption" className="text-destructive">
          {error}
        </Typography>
      )}
    </VStack>
  )
}

// Responsive breakpoint hook
export function useBreakpoint() {
  const [breakpoint, setBreakpoint] = React.useState<'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'>('md')
  
  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 475) setBreakpoint('xs')
      else if (width < 640) setBreakpoint('sm')
      else if (width < 768) setBreakpoint('md')
      else if (width < 1024) setBreakpoint('lg')
      else if (width < 1280) setBreakpoint('xl')
      else setBreakpoint('2xl')
    }
    
    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])
  
  return {
    breakpoint,
    isMobile: breakpoint === 'xs' || breakpoint === 'sm',
    isTablet: breakpoint === 'md',
    isDesktop: breakpoint === 'lg' || breakpoint === 'xl' || breakpoint === '2xl'
  }
}
